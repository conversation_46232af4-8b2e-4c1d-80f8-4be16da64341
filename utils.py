import base64
import gzip
from binascii import Error as Base64Error
from Crypto.Cipher import AES
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
from svrkit.core.oss import OssAttrInc

def base64_decode(encoded_str: str) -> bytes:
    try:
        # 添加必要的填充
        padding = len(encoded_str) % 4
        if padding > 0:
            encoded_str += '=' * (4 - padding)
        
        return base64.b64decode(encoded_str)
    except Base64Error as e:
        raise ValueError(f"无效的 Base64 输入: {e}") from e

def decode_with_pycryptodome(encoded_str: str, secret_key: bytes) -> str:
    try:
        if len(secret_key) not in [16, 24, 32]:
            print(
                f"无效的AES密钥长度: {len(secret_key)}字节，"
                "应为16/24/32字节（对应AES-128/192/256）"
            )
            OssAttrInc(535937,12,1)
            return None
    except Exception as e:
        OssAttrInc(535937,13,1)
        return None
    
    try:
        combined_data = base64_decode(encoded_str)
        
        iv = combined_data[:12]  # 前12字节为IV
        ciphertext_with_tag = combined_data[12:]  # 剩余为密文+标签
        ciphertext = ciphertext_with_tag[:-16]  # 倒数16字节为标签
        tag = ciphertext_with_tag[-16:]
    except Exception as e:
        return None
    
    try:
        cipher = AES.new(secret_key, AES.MODE_GCM, nonce=iv)
        compressed_data = cipher.decrypt_and_verify(ciphertext, tag)
    except ValueError as e:
        print("解密失败（认证标签无效或数据损坏）")
        OssAttrInc(535937,19,1)
        return None
    
    original_bytes = gzip.decompress(compressed_data)
    
    return original_bytes.decode('utf-8').strip()

def load_private_key_from_base64(file_path) -> RSA.RsaKey:
    with open(file_path, "r") as f:
        pem_cleaned = f.read().strip()
        key_bytes = base64_decode(pem_cleaned)
        key = RSA.import_key(key_bytes)
        return key

def decrypt_with_rsa(private_key: RSA.RsaKey, encrypted_data_b64: str) -> bytes:
    try:
        encrypted_bytes = base64_decode(encrypted_data_b64)   
        #print(f"[DEBUG] 加密数据字节长度: {len(encrypted_bytes)}")  # 预期 256
        cipher = PKCS1_v1_5.new(private_key)
        decrypted_bytes = cipher.decrypt(encrypted_bytes, sentinel=None)
    except Exception as e:
        # print(e)
        OssAttrInc(535937,14,1)
        return None

    if decrypted_bytes is None:
        # print("解密失败（密钥不匹配或数据损坏）")
        OssAttrInc(535937,15,1)
        return None
    return decrypted_bytes


def replace_consecutive_stars(s: str) -> str:
    if not s:
        return s
    
    result = []
    count = 0
    
    # 遍历字符串中的每个字符
    for i, char in enumerate(s):
        if char == '*':
            count += 1
            # 如果是最后一个字符且星号数量大于等于3
            if i == len(s) - 1 and count >= 3:
                result.extend(['0'] * (len(result) - count + 1))
        else:
            # 如果之前有连续3个及以上的星号，替换为'0'
            if count >= 3:
                result.extend(['0'] * (len(result) - count + 1))
            else:
                # 否则保留原有的星号
                result.extend(['*'] * count)
            result.append(char)
            count = 0
    
    # 处理字符串末尾的星号
    if count > 0:
        if count >= 3:
            result.append('0')
        else:
            result.extend(['*'] * count)
    
    return ''.join(result)


